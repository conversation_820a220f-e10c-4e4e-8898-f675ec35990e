# FROM node:18-alpine AS deps
# WORKDIR /app
# COPY package.json package-lock.json* ./

# FROM deps as runner
# WORKDIR /app
# COPY . .



# RUN apk add git
# RUN npm install
# RUN npm run build
# EXPOSE 3000
# ENV NODE_ENV production
# CMD ["npm", "start"]

# syntax=docker.io/docker/dockerfile:1

FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* .npmrc* ./

RUN apk add git

RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi


# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED=1
ARG NEXT_PUBLIC_DATA_BASE_URL
ARG NEXTAUTH_SECRET
ARG NEXTAUTH_URL
ARG NEXT_PUBLIC_LANG_LOCALE
ARG NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK
ARG NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK
ARG NEXT_PUBLIC_DASHBOARD_LANG_LOCALES
ARG NEXT_PUBLIC_DASHBOARD_DEFAULT_CURRENCY
ARG NEXT_PUBLIC_IRAN_SERVED_ONLY
ARG NEXT_PUBLIC_DOC_PUBLIC
ARG SENTRY_AUTH_TOKEN
ARG NEXT_PUBLIC_SUPPLIER_USER_CONTEXT
ARG NEXT_PUBLIC_RETAILER_USER_CONTEXT
ARG PROFILES_MAX_UPLOAD_SIZE_MB
ARG PRODUCTS_IMG_MAX_SIZE_MB
ARG NEXT_PUBLIC_GOOGLE_TAGM_ID

RUN apk add git

RUN \
  if [ -f yarn.lock ]; then yarn run build; \
  elif [ -f package-lock.json ]; then npm run build; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build; \
  else echo "Lockfile not found." && exit 1; \
  fi

RUN npm run postinstall

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/config/next-config-js/output
# ENV HOSTNAME="0.0.0.0"
CMD ["node", "server.js"]

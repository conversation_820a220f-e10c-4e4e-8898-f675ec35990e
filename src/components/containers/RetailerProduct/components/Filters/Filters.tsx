import { Icon } from "@iconify/react";
import React from "react";
import { useTranslation } from "react-i18next";
import { useProductFilters } from "./useProductFilters";
import ToggleFilter from "../../../Filters/ToggleFilter";
import DropdownFilter from "../../../Filters/DropdownFilter";
import { handleSetFilter, omitEmptyValues } from "@/utils/helpers";

function Filters() {
  const { t } = useTranslation();
  const { filters, sorts: sortsStates, setFilters } = useProductFilters();
  const { created_at } = sortsStates || {};
  const hasFilters = !!Object.values(omitEmptyValues(filters))?.filter(Boolean)?.length;

  const handleResetAll = () => {
    const keys = !!filters ? Object.keys(filters) : [];

    keys?.forEach(item => setFilters({ [item]: null }, { history: "push" }));
  };

  const onReset = (key: string | string[]) => {
    if (Array.isArray(key) && key?.length) {
      key?.forEach(item => {
        setFilters({ [item]: null });
      });
    } else setFilters({ [key as any]: null }, { history: "push" });
  };

  const RenderFilters = () => {
    return (
      <>
        <ToggleFilter
          title={t("retailerProduct.filters.premium")}
          filterKey="premium"
          initialValue={filters?.premium}
          setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
          onReset={onReset}
        />
        <ToggleFilter
          title={t("retailerProduct.filters.winning")}
          filterKey="winning"
          initialValue={filters?.winning}
          setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
          onReset={onReset}
        />

        <DropdownFilter
          title={t("retailerProduct.filters.sort")}
          filterKey="created_at"
          icon={<Icon icon="solar:sort-outline" width={18} height={18} />}
          initialValue={created_at}
          setFilterValue={(k, v) => handleSetFilter({ key: k, value: v, setFilters })}
          onReset={onReset}
          options={[
            { id: "desc", label: t(`retailerProduct.filters.newest`) },
            { id: "asc", label: t(`retailerProduct.filters.oldest`) }
          ]}
        />

        {hasFilters && (
          <>
            <div className="w-px h-4 bg-v2-border-primary" />

            <div className="text-v2-content-on-action-2 text-xs cursor-pointer" onClick={handleResetAll}>
              {t("removeFilters")}
            </div>
          </>
        )}
      </>
    );
  };

  return (
    <div className="flex items-center gap-2">
      <RenderFilters />
    </div>
  );
}

export default Filters;

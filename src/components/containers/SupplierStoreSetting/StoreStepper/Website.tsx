import { routes } from "@/constants/routes";
import { IntegrationData } from "@/store/apps/meta/types";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import useLanguage from "@/utils/hooks/useLanguage";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import SupplierDynamicForm from "../SupplierDynamicForm";
import ProfileContentContainer from "../../ProfileStepper/ProfileContentContainer";
import MobileAppBar from "../../mobileAppBar/MobileAppBar";

interface IWebsiteProps {
  isLoading: boolean;
  integrations?: IntegrationData[];
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
  setIsConnecting?: (value: boolean) => void;
  setIntegrationKey?: (value: string) => void;
  onBack: () => void;
  setInProgress?: () => void;
}

function Website({
  isLoading,
  integrations,
  supplierStore,
  setIsConnecting,
  isStoreSingleLoading,
  setIntegrationKey,
  onBack,
  setInProgress
}: IWebsiteProps) {
  const { t } = useTranslation();
  const [currentLang] = useLanguage();
  const router = useRouter();
  const makePath = useRoleBasePath();
  const [selectWebsite, setSelectWebsite] = useState("");

  const integration = integrations?.find(item => item?.key === selectWebsite);

  const configureForm = integration?.configForm || {};

  const handleSelectWebsite = (id: string) => {
    setSelectWebsite(id);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <>
      <MobileAppBar title={t("connectStore")} hasBack onBack={onBack} />
      <ProfileContentContainer containerClassName="!py-4">
        <div className=" xmd:pb-12 ">
          <div className="xmd:flex hidden items-center w-fit gap-2 mb-4 cursor-pointer" onClick={onBack}>
            <Icon icon="solar:arrow-right-outline" className="size-5 text-v2-content-tertiary " />

            <p className="text-center text-v2-content-tertiary text-body2-medium  font-semibold flex-1">{t("back")}</p>
          </div>

          <div className="xmd:border xmd:border-solid xmd:border-v2-border-primary xmd:rounded-[9px] xmd:mb-4  relative">
            <div className="xmd:p-6 p-0 pb-4 border-b border-b-v2-border-primary flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Icon icon="solar:folder-path-connect-outline" className="size-5" />
                <span className="text-body1-medium text-v2-content-primary !font-semibold">
                  {t("store.productStore.title")}
                </span>
              </div>

              <span className="text-body3-medium text-v2-content-tertiary">{t("store.productStore.subtitle")}</span>
            </div>

            <div className="xmd:p-6 p-0">
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <CircularProgress />
                </div>
              ) : (
                <>
                  <div className="xmd:mt-0 mt-4">
                    <span className="text-body1-medium text-gray-999">{t("store.whichPlatform")}</span>
                  </div>

                  <div className="flex gap-4 items-center mt-4 flex-wrap">
                    {integrations?.map(item => (
                      <div
                        className={twMerge(
                          "px-4 py-4 cursor-pointer flex-1 w-1/2 flex items-center justify-between border border-solid border-gray-50 rounded-lg ",
                          item.key === selectWebsite && " border-purple-500"
                        )}
                        key={item?.key}
                        onClick={() => {
                          handleSelectWebsite(item.key);
                          setIntegrationKey?.(item?.key);
                        }}
                      >
                        <div className="flex items-center gap-2">
                          {/* <Avatar src={item?.logo} className="w-[37px] h-[42px] rounded-sm" /> */}
                          {!!item?.logo && (
                            <div
                              dangerouslySetInnerHTML={{ __html: item?.logo }}
                              className="w-[37px] h-[42px] [&>svg]:size-[37px] rounded-sm"
                            />
                          )}

                          <p className="text-gray-999 text-body3-medium">
                            {item?.name?.find(l => l.iso === currentLang?.value)?.text}
                          </p>
                        </div>

                        {item.key === selectWebsite ? (
                          <div className="size-5 bg-purple-500 border border-solid border-purple-500 rounded-full flex items-center justify-center">
                            <Icon icon="mdi:tick" className="text-[white] size-3.5" />
                          </div>
                        ) : (
                          <div className="border border-solid border-gray-50 size-5 rounded-full" />
                        )}
                      </div>
                    ))}
                  </div>
                </>
              )}

              <div className="mt-4">
                {!!integration?.key && (
                  <SupplierDynamicForm
                    configForm={configureForm}
                    integration={integration}
                    id={integration?.key}
                    isStoreSingleLoading={isStoreSingleLoading}
                    supplierStore={supplierStore}
                    isLoading={isLoading}
                    setIsConnecting={setIsConnecting}
                    handleSubmit={setInProgress}
                  />
                )}
              </div>

              <div className="bg-v2-surface-warining-1 flex items-center gap-1.5 p-2.5 rounded-md xmd:mt-32 mt-auto">
                <Icon icon="solar:info-circle-outline" className="size-5 text-warning-800 shrink-0" />
                <span className="text-body4-medium text-warning-800">{t("store.alertOutOfPlatform")}</span>
              </div>
            </div>
          </div>
        </div>
      </ProfileContentContainer>
    </>
  );
}

export default Website;

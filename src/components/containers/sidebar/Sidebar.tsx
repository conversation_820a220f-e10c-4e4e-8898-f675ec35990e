import "./Sidebar.css";

import { Box, Chip } from "@mui/material";
import { Drawer } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useTheme } from "@mui/material/styles";
import SidebarItems from "./SidebarItems";

import Scrollbar from "@/components/ui/custom-scroll/Scrollbar";
import React, { ForwardedRef, useMemo } from "react";
import { Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { routes } from "@/constants/routes/index";
import Link from "next/link";
import { themeCustomizer } from "@/utils/theme";
import { RETAILER_USER_TYPE } from "@/constants";
import NavItem from "./NavItem";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { fixedMenuItems, menuitems, MenuitemsType } from "./MenuItems";
import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";

import { usePathname, useSearchParams } from "next/navigation";
import { USERTYPES } from "@/constants/userTypes";
import Image from "next/image";
import useSessionStore from "@/store/zustand/sessionStore";
import ContactInfo from "./ContactInfo";

function Sidebar(
  {
    isOpen,
    isHover,
    setIshover,
    setIsOpen
  }: {
    isOpen: boolean;
    isHover: boolean;
    setIshover: (value: boolean) => void;
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  },
  _ref: ForwardedRef<{ toggle: () => void }>
) {
  const { t } = useTranslation();
  // const { data: session } = useSession();
  const { user_type } = useSessionStore();

  const userTypeTitle = user_type === RETAILER_USER_TYPE ? t("retailer.retailer") : t("retailerImport.supplier");
  // const [isOpen, setIsOpen] = useState(false);
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const MiniSidebarWidth = themeCustomizer.MiniSidebarWidth;
  const SidebarWidth = themeCustomizer?.SidebarWidth;
  const path = usePathname();
  const searchParams = useSearchParams();
  const makePath = useRoleBasePath();

  const theme = useTheme();
  const toggleWidth = isOpen && !isHover ? MiniSidebarWidth : SidebarWidth;
  const fixedItems = fixedMenuItems({ makePath })?.filter(item => item?.for?.includes(user_type as USERTYPES));
  const Menuitems = menuitems({ makePath })?.filter(item => item?.for?.includes(user_type as USERTYPES));

  const isActive = (item: MenuitemsType) => {
    const pathname = `${path}${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

    const whiteList = ["notifAndChat"];
    const isWhiteList = whiteList?.some(val => val === item?.name);

    if (item?.children?.length) {
      return !!item.children?.find(val => val?.href === pathname);
    }
    if (item?.name === "home") {
      if (item.href.includes(pathname)) return true;
      return false;
    }
    if (isWhiteList) {
      return path === item.href ? true : false;
    }

    return pathname?.includes(item.href);
  };

  const extraItems = useMemo(
    () => (
      <Box className="sx-sidebaritems-7677">
        <Typography variant="caption" color={theme.palette.grey["400"]} className="sidebar-extra-items-header">
          {t("settings.otherSections")}
        </Typography>
        {fixedItems.map(item => (
          <NavItem
            key={item.id}
            item={item}
            isActive={isActive(item)}
            level={1}
            hideMenu={isOpen && lgUp && !isHover}
            pathDirect={path}
            onClick={() => {}}
          />
        ))}
      </Box>
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isOpen, isHover, isActive]
  );
  // useImperativeHandle(ref, () => ({
  //   toggle: () => {
  //     setIsOpen(prev => !prev);
  //   }
  // }));

  const onHoverEnter = () => {
    if (isOpen) {
      setIshover(true);
    }
  };

  const onHoverLeave = () => {
    setIshover(false);
  };

  if (lgUp) {
    return (
      <div className="sidebar-container sticky top-6 right-0 w-[270px] h-fit bg-cards rounded-lg min-h-[calc(100vh-48px)] flex flex-col">
        <>
          {/* ------------------------------------------- */}
          {/* Sidebar Box */}
          {/* ------------------------------------------- */}
          <Box id="sx-sidebar-7544">
            <Box px={isOpen ? 1.5 : 4} pt={7} id="sx-sidebar-7554">
              <Link href={routes.home} className="sidebar-header-title">
                <Image src="/images/svgs/drophub-logo.svg" alt="dophub-logo" width={150} height={35} />
              </Link>
              <Chip label={userTypeTitle} variant="outlined" color="secondary" className="sidebar-header-title-chip" />
            </Box>
          </Box>
          {/* <div className="h-full bg-[red] "> */}
          <div className="flex flex-col flex-1" dir="ltr">
            {/* <div className="flex flex-col h-full " dir="rtl"> */}
            {/* ------------------------------------------- */}
            {/* Sidebar Items */}
            {/* ------------------------------------------- */}
            <div dir="rtl">
              <SidebarItems items={Menuitems} isHover={isHover} isOpen={isOpen} isActive={isActive} />
            </div>
            <div dir="rtl">{extraItems}</div>
            <div className="mt-auto m-4 " dir="rtl">
              <ContactInfo />
            </div>
            {/* </div> */}
          </div>
          {/* </div> */}
        </>
      </div>
    );
  }

  return (
    <>
      <Drawer
        anchor="left"
        open={isOpen}
        onClose={() => setIsOpen(false)}
        variant="temporary"
        dir="ltr"
        PaperProps={{
          className: "sidebar-paper"
        }}
      >
        <div className="flex flex-col h-full" dir="rtl">
          <Box px={4} py={4} marginBottom={2} id="sx-sidebar-7610" onClick={() => setIsOpen(false)}>
            <Link href={routes.home} className="sidebar-header-title">
              {/* <Typography variant="h4">{t("companyTitle")}</Typography> */}
              <Image src="/images/svgs/drophub-logo.svg" alt="dophub-logo" width={150} height={35} />
            </Link>
            {/* <Logo /> */}
            <Chip label={userTypeTitle} variant="outlined" color="secondary" className="sidebar-header-title-chip" />
          </Box>
          <SidebarItems items={Menuitems} isHover={isHover} isOpen={isOpen} setIsOpen={setIsOpen} isActive={isActive} />
          {extraItems}
          <div className=" mt-auto m-4">
            <ContactInfo />
          </div>{" "}
        </div>
      </Drawer>
    </>
  );
}
export default React.memo(React.forwardRef(Sidebar));

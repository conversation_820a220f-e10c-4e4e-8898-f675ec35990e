import React from "react";
import { useTranslation } from "react-i18next";
import { Authenticity, Condition } from "@/store/apps/product/types";
import { twMerge } from "tailwind-merge";

interface IConditionProps {
  condition: Condition;
}

function ConditionBadge({ condition }: IConditionProps) {
  const { t } = useTranslation();

  const conditionBadge = {
    New: {
      bg: "#EDFDF4",
      color: "#18B466",
      title: t("product.new")
    },
    Refurbished: {
      bg: "#F3F4F6",
      color: "#4D5761",
      title: t("product.refurbished")
    },
    Used: {
      bg: "#FFEBD8",
      color: "#884202",
      title: t("product.used")
    }
  };

  return (
    <>
      {condition && (
        <div
          className={twMerge("rounded px-1.5 py-0.5 text-caption-medium w-fit ")}
          style={{
            background: conditionBadge?.[condition]?.bg,
            color: conditionBadge?.[condition]?.color
          }}
        >
          {conditionBadge?.[condition]?.title}
        </div>
      )}
    </>
  );
}

export default ConditionBadge;

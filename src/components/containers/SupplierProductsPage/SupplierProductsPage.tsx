import React, { useEffect, useState } from "react";
import RetailerProduct from "../RetailerProduct/RetailerProduct";
import { useParams } from "next/navigation";
import { IProductSupplier } from "@/store/apps/product/types";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import CustomBreadcrumb from "@/components/ui/CustomBreadcrumb/CustomBreadcrumb";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import Header from "./Header";
import { useMediaQuery } from "@mui/system";
import { Theme } from "@mui/material";
import MobileHeader from "./MobileHeader";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import CustomTabsFilled from "@/components/ui/CustomTabsFilled/CustomTabsFilled";
import { useProductFilters } from "../RetailerProduct/components/Filters/useProductFilters";
import { handleSetFilter } from "@/utils/helpers";

function SupplierProductsPage({ isSupplierView = false }: { isSupplierView?: boolean }) {
  const { t } = useTranslation();
  const params = useParams();
  const makePath = useRoleBasePath();
  const [productName, setProductName] = useState("");
  const [supplierData, setSupplierData] = useState<IProductSupplier | undefined>(undefined);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const BCrumb = [
    {
      title: t("retailerProduct.marketplace"),
      to: makePath(routes.product)
    },
    ...(supplierData?.name
      ? [
          {
            title: supplierData?.name
          }
        ]
      : [])
  ];

  const tabs = [
    {
      id: 1,
      title: t("retailerProduct.products"),
      value: "products"
    },
    {
      id: 2,
      title: t("retailerProduct.comments"),
      value: "published"
    }
  ];

  const { setFilters } = useProductFilters();

  useEffect(() => {
    if (params?.id) {
      // setFilters({ supplier: [params?.id as string] }, { history: "push" });
      handleSetFilter({ key: "supplier", value: [params?.id as string], setFilters });
    }
  }, [params?.id]);

  return (
    <>
      {isMobile && <MobileAppBar title={t("retailerProduct.shopPage")} hasBack className="border-b border-gray-50" />}
      <CustomCardContent className="h-full flex flex-col gap-4 !rounded-none md:!rounded-lg">
        {/* ------------------------------- breadcrumb ------------------------------- */}
        {!isMobile && !isSupplierView && <CustomBreadcrumb items={BCrumb} className="!mb-0 !py-0" />}
        {/* --------------------------------- header --------------------------------- */}
        {isMobile ? (
          <MobileHeader
            id={supplierData?.id}
            cover={supplierData?.cover}
            logo={supplierData?.logo}
            name={supplierData?.name}
            productName={productName}
            isSupplierView={isSupplierView}
          />
        ) : (
          <Header
            id={supplierData?.id}
            cover={supplierData?.cover}
            logo={supplierData?.logo}
            name={supplierData?.name}
            productName={productName}
            isSupplierView={isSupplierView}
          />
        )}

        <CustomTabsFilled items={tabs} value="products" />
        <RetailerProduct
          showFilters={false}
          setSupplierData={data => {
            setSupplierData(data);
          }}
          isSupplierView={isSupplierView}
        />
      </CustomCardContent>
    </>
  );
}

export default SupplierProductsPage;

"use client";

import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Avatar, Divider, MenuItem, SelectChangeEvent, Stack, Theme, useMediaQuery } from "@mui/material";
import { Table } from "@mui/material";
import { TableBody } from "@mui/material";
import { TableCell } from "@mui/material";
import { TableHead } from "@mui/material";
import { TableRow } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import { TableContainer } from "@mui/material";
import { Box } from "@mui/material";
import { CircularProgress } from "@mui/material";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import { Icon } from "@iconify/react";
import useModal from "@/utils/hooks/useModal";
import Filters from "./Filters/Filters";
import {
  useDeleteRetailderProductMutation,
  useGetRetailerImportsListQuery,
  usePostRetailderProductPushMutation,
  usePostRetailderProductUnPushMutation
} from "@/store/apps/retailerProduct";
import { usePathname, useRouter } from "next/navigation";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { generateBackendFilters, generateBackendSorts } from "@/utils/services/transformers";
import Button from "@/components/ui/Button";
import { useFiltersState } from "./Filters/useFiltersState";
import { calculateTotalCount, handleSetFilter, omitEmptyValues } from "@/utils/helpers";
import { useSelector } from "@/store/hooks";
import { AppState } from "@/store/store";
import { shallowEqual } from "react-redux";
import Collapse2 from "@/components/ui/Collapse2/Collapse2";
import Image from "next/image";
import useCurrency from "@/utils/hooks/useCurrency";
import { calcProfitAmount } from "@/components/containers/productDetailPage/utils";
import Edit from "./EditPage/EditPage";
import CustomMenu from "@/components/ui/CustomMenu/CustomMenu";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import { twMerge } from "tailwind-merge";
import Input from "@/components/ui/inputs/Input";
import { debounce } from "lodash";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { ListInfiniteScroll } from "@/components/ui/ListInfiniteScroll";
import { TRetailerImportListData } from "@/store/apps/retailerProduct/types";
import ProductEmpty from "../productEmpty/ProductEmpty";

function Products() {
  /* ---------------------------------- hooks --------------------------------- */
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const pathname = usePathname();
  const router = useRouter();

  const [hasCheckbox, setHasCheckbox] = useState(false);
  // const [hasError, setHasError] = useState(false);

  const { data: retailerStore } = useSelector(
    (state: AppState) => ({
      data: state?.retailerStore?.data
    }),
    shallowEqual
  );
  const [{ render: renderPrice, symbol }] = useCurrency();
  const { onChatStart, isConversationLoading } = useConversationStart();

  /* --------------------------------- states --------------------------------- */
  const { filters, setFilters } = useFiltersState();
  const { page, pageSize, created_at, updated_at, ...restFilters } = filters || {};
  const nonEmptyFilters = useMemo(() => omitEmptyValues(restFilters), [restFilters]);

  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const isDraft = pathname.includes(routes.retailerProductsDrafts);

  const editFormRef = useRef<any>(null);

  const handleSaveClick = () => {
    // You can programmatically submit the form from the parent
    if (editFormRef.current) {
      editFormRef.current.submitForm();
    }
  };

  const handleOnChange = useCallback(
    debounce((value: string) => {
      // setFilters({ title: value, page: 1 }, { history: "push" });
      handleSetFilter({ key: "title", value, setFilters });
    }, 2000),
    []
  );
  const [internalValue, setInternalValue] = useState<string | undefined>(filters?.title || undefined);

  const finalFilters = generateBackendFilters({ ...nonEmptyFilters, draft: isDraft });
  const sorts = generateBackendSorts(
    omitEmptyValues({
      created_at: (created_at as any) || undefined,
      updated_at: (updated_at as any) || undefined
    })
  );

  const queryParts = [
    `page_size=${filters?.pageSize}`,
    `page=${filters?.page}`,
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ].filter(part => part !== "");
  const queryString = queryParts.join("&");

  /* ----------------------------------- rtk ---------------------------------- */
  const {
    data: productData,
    isLoading: isProductLoading,
    isFetching: isProductFetching,
    isError: isProductError
  } = useGetRetailerImportsListQuery(queryString || "", {
    skip: !filters?.page,
    refetchOnMountOrArgChange: true
  });
  //   const isProductLoading = true;
  const [pushProducts] = usePostRetailderProductPushMutation();
  const [unpushProducts] = usePostRetailderProductUnPushMutation();
  const [deleteProducts] = useDeleteRetailderProductMutation();
  const totalCount = productData?.pagination?.total ?? 0;

  const hasNextPage = calculateTotalCount({ pageSize, totalCount }) > page;

  const [products, setProducts] = useState<TRetailerImportListData["data"]>([]);

  useEffect(() => {
    if (!isMobile) return;
    if (productData?.data?.length && !isProductLoading) {
      if (page === 1) setProducts(productData?.data);
      else setProducts(prevOrders => [...prevOrders, ...productData?.data]);
    } else setProducts([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productData?.data, isProductLoading, isMobile]);

  /* -------------------------------- functions ------------------------------- */
  const handleChangePage = (_event: unknown, newPage: number) => {
    setFilters({ page: newPage }, { history: "push" });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    setFilters({ pageSize: event.target.value as number }, { history: "push" });
    setFilters({ page: 1 }, { history: "push" });
  };

  const handlePublish = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      pushProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const handleUnPublish = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      unpushProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const handleDelete = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      deleteProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const onClickPublish = (id: string[]) => {
    showModal({
      title: t("retailerProduct.publishModalTitle"),
      subTitle: t("retailerProduct.publishModalSubtitle"),
      icon: "/images/svgs/publish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.yesPublish"),
          variant: "primary",
          onClick: () => handlePublish(id)
        }
      ]
    });
  };

  const onClickUnPublish = (id: string[]) => {
    showModal({
      title: t("retailerProduct.unpublishModalTitle"),
      subTitle: t("retailerProduct.unpublishModalSubtitle"),
      icon: "/images/svgs/unpublish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.unpublish"),
          variant: "destructivePrimary",
          onClick: () => handleUnPublish(id)
        }
      ]
    });
  };

  const onClickDelete = (id: string[]) => {
    showModal({
      title: t("retailerProduct.removePublishModalTitle"),
      subTitle: t("retailerProduct.removePublishModalSubTitle"),
      icon: "/images/svgs/removePublish.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.yesUnpublish"),
          variant: "destructivePrimary",
          onClick: () => handleDelete(id)
        }
      ]
    });
  };

  const handleCheckAllItems = (checked: boolean) => {
    const ids = productData?.data
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  const onClickThreeDots = (item: TRetailerImportListData["data"][number]) => {
    showModal({
      body: (
        <div>
          <>
            <p className="truncate mt-3 w-[90%] text-body4-medium mb-3">{item?.title}</p>
            <p className="pb-3.5 pt-1.5 text-[13px]">{t("retailerProduct.buySample")}</p>
            <Divider className="bg-gray-50 !my-0" />
            <Link href={`${makePath(routes.product)}/${item.originProduct?.id}`} target="_blank">
              <p className="py-3.5 text-[13px]">{t("retailerProduct.productPage")}</p>
            </Link>
            <Divider className="bg-gray-50 !my-0" />
            <p
              className="py-3.5 text-[13px]"
              onClick={() =>
                !isConversationLoading &&
                onChatStart({
                  content: t("chats.product", { name: item?.title }),
                  partnerId: item?.originProduct?.supplierId
                })
              }
            >
              {t("retailerProduct.contactWithSupplier")}
            </p>
            {/* {!isDraft && (
              <>
                <Divider className="bg-gray-50 !my-0" />
                <p
                  className="py-3.5 text-[13px] text-v2-content-on-error-2"
                  onClick={() => onClickUnPublish([item?.id])}
                >
                  {t("retailerProduct.unPublish")}
                </p>
              </>
            )} */}

            <Divider className="bg-gray-50 !my-0" />
            <p
              className="py-3.5 text-[13px] text-v2-content-on-error-2"
              onClick={() => !isConversationLoading && onClickDelete([item?.id])}
            >
              {t("retailerProduct.removeFromStore")}
            </p>
          </>
        </div>
      )
    });
  };

  if (isProductLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="h-full">
        <div className="flex flex-col px-4 w-[100vw] ">
          {!!products?.length && (
            <div className="mb-3">
              <Input
                startAdornment={
                  <Icon
                    icon="solar:magnifer-linear"
                    width="1.1rem"
                    color="#ADADAD"
                    height="1.1rem"
                    className="ml-1.5"
                  />
                }
                inputSize="sm"
                variant="filled"
                className="max-h-10 "
                // inputParentClassName="bg-v2-surface-primary"
                rootClassName=" shrink-0 "
                inputParentClassName="!bg-v2-surface-primary"
                value={internalValue || undefined}
                placeholder={`${t("chats.searchQuery")} ...`}
                onChange={e => {
                  handleOnChange(e.target.value);
                  setInternalValue(e.target.value);
                }}
              />
            </div>
          )}

          {!!products?.length && (
            <div className="mb-5">
              {hasCheckbox ? (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CustomCheckbox
                      className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                      indeterminate={!!checkedIds?.length && checkedIds?.length !== productData?.data?.length}
                      onChange={(_e, checked) => handleCheckAllItems(checked)}
                      checked={!!checkedIds?.length && checkedIds?.length === productData?.data?.length}
                    />

                    <span className="text-v2-content-primary text-body3-medium whitespace-nowrap">
                      {t("selectAll")}
                    </span>

                    {!!checkedIds?.length && (
                      <span className="text-body3-medium text-v2-content-tertiary whitespace-nowrap">
                        ({t("selected")} {checkedIds?.length})
                      </span>
                    )}
                  </div>
                  <p
                    className="text-v2-content-on-action-2 text-body3-medium cursor-pointer"
                    onClick={() => setHasCheckbox(false)}
                  >
                    {t("done")}
                  </p>
                </div>
              ) : (
                <p
                  className="text-v2-content-on-action-2 text-body3-medium cursor-pointer text-end"
                  onClick={() => setHasCheckbox(true)}
                >
                  {t("select")}
                </p>
              )}
            </div>
          )}

          <div className="flex flex-col gap-2 w-full flex-1 ">
            {!products?.length ? (
              <ProductEmpty isDraft={isDraft} hasSearchValue={!!internalValue?.length} />
            ) : (
              products?.map((item, index) => {
                const retailerProfitAmount = calcProfitAmount({
                  commission: item?.originProduct?.cheapestVariant?.commission,
                  retailPrice: item?.originProduct?.cheapestVariant?.retailPrice
                });

                return (
                  <div
                    key={item?.id}
                    className={twMerge(
                      "grid gap-2.5 w-full",
                      hasCheckbox ? "grid-cols-[30px_minmax(0,1fr)]" : "grid-cols-1"
                    )}
                  >
                    {hasCheckbox && (
                      <div>
                        <CustomCheckbox
                          onChange={(_e, _checked) => handleCheckItem(item.id)}
                          checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                          className="p-0 !m-0"
                          checkboxClassName="p-0.5"
                        />
                      </div>
                    )}

                    <Collapse2
                      variant={isMobile ? "secondary" : "primary"}
                      key={item?.id}
                      initialIsOpen={isDraft && index === 0 && filters?.page === 1}
                      startAdornment={
                        <div className="flex gap-3 items-center ms-1">
                          <div className="bg-v2-surface-secondary size-[64px] rounded-md relative">
                            <Image
                              src={item?.originProduct?.cover?.url}
                              alt={item?.originProduct?.cover?.alt || item.title}
                              fill
                              className="object-contain"
                            />
                          </div>

                          <div className="flex flex-col gap-1.5">
                            <p className="text-sm font-medium text-v2-content-primary line-clamp-1  ">{item.title}</p>
                            <div className="flex items-center gap-3 flex-wrap">
                              <div className="!pr-0 text-v2-content-tertiary text-xs font-normal flex flex-col gap-1">
                                <p>{t("retailerImport.salesPrice")} : </p>
                                <p className="font-medium text-v2-content-primary">
                                  {renderPrice(item?.originProduct?.cheapestVariant?.retailPrice)}
                                </p>
                              </div>

                              <Divider
                                orientation="vertical"
                                variant="middle"
                                flexItem
                                className=" border-v2-border-primary h-4 mt-3"
                              />

                              {!!retailerProfitAmount && (
                                <>
                                  <div className="text-v2-content-tertiary text-xs font-normal flex flex-col gap-1">
                                    <p>{t("retailerProduct.yourProfit")} : </p>
                                    <p className="font-medium text-v2-content-primary">
                                      {renderPrice(retailerProfitAmount)}
                                    </p>
                                  </div>

                                  <Divider
                                    orientation="vertical"
                                    variant="middle"
                                    flexItem
                                    className=" border-v2-border-primary h-4 mt-3"
                                  />
                                </>
                              )}

                              <div className="text-v2-content-tertiary text-xs font-normal flex flex-col gap-1">
                                <p>{t("retailerProduct.supplier")} : </p>
                                <p className="font-medium">{item?.originProduct?.supplier?.name}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      }
                      endAdornment={
                        <div className="flex items-center gap-3 ">
                          {isDraft && (
                            <Button
                              variant="primary"
                              className="xmd:flex-auto flex-1"
                              onClick={() => {
                                // handleSaveClick();
                                handleSaveClick();

                                setTimeout(() => {
                                  if (!Object.keys(editFormRef.current.errors)?.length) onClickPublish([item.id]);
                                }, 0);
                              }}
                            >
                              {t("retailerProduct.addToShop")}
                            </Button>
                          )}

                          <div
                            className="flex items-center justify-center p-2.5 border border-v2-border-primary rounded-lg cursor-pointer"
                            onClick={() => onClickThreeDots(item)}
                          >
                            <Icon icon="solar:menu-dots-bold" width={20} height={20} className="rotate-90" />
                          </div>
                        </div>
                      }
                      rootClassName="rounded-lg bg-v2-surface-primary aria-expanded:pt-[11px] transition-all"
                      className="py-4 px-5 !bg-transparent aria-expanded:border-b aria-expanded:border-v2-border-secondary "
                    >
                      <Edit ref={editFormRef} product={item} />
                    </Collapse2>
                  </div>
                );
              })
            )}
          </div>
        </div>

        {isDraft && !!checkedIds?.length && (
          <BottomAction containerClassName="!z-[999999] gap-6">
            <Button
              variant="destructiveSecondaryGray"
              className="text-v2-content-on-error-2"
              onClick={() => onClickDelete(checkedIds)}
            >
              <Icon icon="solar:trash-bin-minimalistic-outline" width={20} height={20} />
              حذف محصولات
            </Button>
            <Button
              variant="destructiveSecondaryGray"
              className="text-v2-content-secondary"
              onClick={() => onClickPublish(checkedIds)}
            >
              <Icon icon="ph:plus" width={20} height={20} />
              اضافە بە فروشگاه
            </Button>
          </BottomAction>
        )}

        {hasNextPage && products?.length >= 10 && (
          <ListInfiniteScroll hasNextPage={hasNextPage} fetchNextPage={() => setFilters({ page: page + 1 })} />
        )}
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {productData?.data?.length && (
        <div className="mb-4 ">
          <Filters
            {...{
              handleCheckAllItems,
              totalCount,
              checkedIds,
              onClickDelete,
              onClickPublish,
              onClickUnPublish,
              isDraft,
              productData
            }}
          />
        </div>
      )}

      <div className="flex flex-col gap-2">
        {!productData?.data?.length ? (
          <ProductEmpty isDraft={isDraft} hasSearchValue={!!internalValue?.length} />
        ) : (
          productData?.data?.map((item, index) => {
            const retailerProfitAmount = calcProfitAmount({
              commission: item?.originProduct?.cheapestVariant?.commission,
              retailPrice: item?.originProduct?.cheapestVariant?.retailPrice
            });

            return (
              <Collapse2
                variant={isMobile ? "secondary" : "primary"}
                key={item?.id}
                initialIsOpen={isDraft && index === 0 && filters?.page === 1}
                startAdornment={
                  <div className="flex gap-3 items-center ms-1">
                    <div>
                      <CustomCheckbox
                        onChange={(_e, _checked) => handleCheckItem(item.id)}
                        checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                        className="p-0 !m-0"
                        checkboxClassName="p-0.5"
                      />
                    </div>
                    <div className="bg-v2-surface-secondary size-[46px] rounded-md relative">
                      <Image
                        src={item?.originProduct?.cover?.url}
                        alt={item?.originProduct?.cover?.alt || item.title}
                        fill
                        className="object-contain"
                      />
                    </div>

                    <div className="flex flex-col gap-1.5">
                      <div className="text-sm font-medium text-v2-content-primary">{item.title}</div>
                      <div className="flex items-center divide-x  divide-x-reverse  divide-gray-50 *:px-3">
                        <div className="!pr-0 text-v2-content-tertiary text-xs font-normal">
                          {t("retailerImport.salesPrice")}:{" "}
                          <span className="font-medium text-v2-content-primary">
                            {renderPrice(item?.originProduct?.cheapestVariant?.retailPrice)}
                          </span>
                        </div>
                        {!!retailerProfitAmount && (
                          <div className="text-v2-content-tertiary text-xs font-normal">
                            {t("retailerProduct.yourProfit")}:{" "}
                            <span className="font-medium text-v2-content-primary">
                              {renderPrice(retailerProfitAmount)}
                            </span>
                          </div>
                        )}
                        <div className="text-v2-content-tertiary text-xs font-normal">
                          {t("retailerProduct.supplier")}:{" "}
                          <span className="font-medium">{item?.originProduct?.supplier?.name}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                }
                endAdornment={
                  <div className="flex items-center gap-3 xmd:flex-row flex-row-reverse">
                    {/* {hasError && (
                      <div className="flex items-center px-2 py-1 gap-1 rounded bg-v2-surface-error">
                        <Icon icon="solar:danger-bold" className="text-v2-content-on-error-2 size-4" />
                        <span className="text-v2-content-on-error-2 text-body4-medium">{t("error")}</span>
                      </div>
                    )} */}
                    <CustomMenu
                      content={
                        <>
                          <MenuItem className="pb-3.5 pt-1.5 text-[13px]">{t("retailerProduct.buySample")}</MenuItem>

                          <Divider className="bg-gray-50 !my-0" />

                          <Link href={`${makePath(routes.product)}/${item.originProduct?.id}`} target="_blank">
                            <MenuItem className="py-3.5 text-[13px]">{t("retailerProduct.productPage")}</MenuItem>
                          </Link>

                          <Divider className="bg-gray-50 !my-0" />

                          <MenuItem
                            className="py-3.5 text-[13px]"
                            onClick={() =>
                              !isConversationLoading &&
                              onChatStart({
                                content: t("chats.product", { name: item?.title }),
                                partnerId: item?.originProduct?.supplierId
                              })
                            }
                          >
                            {t("retailerProduct.contactWithSupplier")}
                          </MenuItem>

                          {/* {!isDraft && (
                            <>
                              <Divider className="bg-gray-50 !my-0" />

                              <MenuItem
                                className="py-3.5 text-[13px] text-v2-content-on-error-2"
                                onClick={() => onClickUnPublish([item?.id])}
                              >
                                {t("retailerProduct.unPublish")}
                              </MenuItem>
                            </>
                          )} */}

                          <Divider className="bg-gray-50 !my-0" />

                          <MenuItem
                            className="py-3.5 text-[13px] text-v2-content-on-error-2"
                            onClick={() => !isConversationLoading && onClickDelete([item?.id])}
                          >
                            {t("retailerProduct.removeFromStore")}
                          </MenuItem>
                        </>
                      }
                    >
                      <div className="flex items-center justify-center p-2.5 border border-v2-border-primary rounded-lg cursor-pointer">
                        <Icon icon="solar:menu-dots-bold" width={20} height={20} className="rotate-90" />
                      </div>
                    </CustomMenu>

                    <>
                      {isDraft && (
                        <Button
                          variant="primary"
                          className="xmd:flex-auto flex-1"
                          onClick={() => {
                            // handleSaveClick();
                            handleSaveClick();

                            setTimeout(() => {
                              if (!Object.keys(editFormRef.current.errors)?.length) onClickPublish([item.id]);
                            }, 0);

                            // if (!editFormRef.current?.errors) onClickPublish([item.id]);
                          }}
                        >
                          {t("retailerProduct.addToShop")}
                        </Button>
                      )}
                    </>
                  </div>
                }
                rootClassName="rounded-lg bg-v2-surface-primary aria-expanded:pt-[11px] transition-all"
                className="py-4 px-5 !bg-transparent aria-expanded:border-b aria-expanded:border-v2-border-secondary "
              >
                <Edit ref={editFormRef} product={item} />
              </Collapse2>
            );
          })
        )}
      </div>

      {!!totalCount && totalCount >= 10 && (
        <div className="bg-v2-surface-primary mt-2 py-2 px-6 rounded-lg">
          {productData?.data && (
            <CustomTablePagination
              rowsPerPageOptions={[10, 50, 100, 200]}
              count={totalCount}
              rowsPerPage={filters?.pageSize}
              page={filters?.page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage={t("product.rowPerPage")}
            />
          )}
        </div>
      )}
    </div>
  );
}

export default Products;

// if (isMobile) {
//   return <div>mobile</div>;
//   // return (
//   //   <ProductsTableMobile
//   //     {...{
//   //       productData,
//   //       isDraft,
//   //       onClickPublish,
//   //       onClickUnPublish,
//   //       onClickDelete,
//   //       totalCount,
//   //       pageSize: filters?.pageSize,
//   //       page: filters?.page,
//   //       handleChangePage,
//   //       handleChangeRowsPerPage,
//   //       isLoading: isProductLoading || isProductFetching,
//   //       hasFilters: !finalFilters
//   //     }}
//   //   />
//   // );
// }

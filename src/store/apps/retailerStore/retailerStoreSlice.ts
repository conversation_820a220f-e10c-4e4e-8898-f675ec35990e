import { STORE_ID } from "@/constants/cookies";
import { create } from "zustand";
import Cookies from "js-cookie";
import { IntegrationDataItem } from "../retailer/types";

export interface RetailerStoreState {
  selectedRetailerStoreId: string | null;
  data: IntegrationDataItem | null;
  isError: boolean;
  isLoading: boolean;
}

interface RetailerStoreActions {
  setRetailerStoreId: (id: string) => void;
  getRetailerStoreId: () => void;
  setRetailerStoreData: (data: IntegrationDataItem | null) => void;
  setIsError: (isError: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
}

type RetailerStore = RetailerStoreState & RetailerStoreActions;

const useRetailerStore = create<RetailerStore>(set => ({
  // Initial state
  selectedRetailerStoreId: null,
  data: null,
  isError: false,
  isLoading: false,

  // Actions
  setRetailerStoreId: (id: string) => {
    set({ selectedRetailerStoreId: id });
    Cookies.set(STORE_ID, id);
  },

  getRetailerStoreId: () => {
    const id = Cookies.get(STORE_ID);
    if (id) {
      set({ selectedRetailerStoreId: id });
    }
  },

  setRetailerStoreData: (data: IntegrationDataItem | null) => {
    set({ data });
  },

  setIsError: (isError: boolean) => {
    set({ isError });
  },

  setIsLoading: (isLoading: boolean) => {
    set({ isLoading });
  }
}));

// Redux-compatible action creators that return action objects
export const setRetailerStoreId = (id: string) => {
  useRetailerStore.getState().setRetailerStoreId(id);
  return { type: "retailerStore/setRetailerStoreId", payload: id };
};

export const getRetailerStoreId = () => {
  useRetailerStore.getState().getRetailerStoreId();
  return { type: "retailerStore/getRetailerStoreId" };
};

export const setRetailerStoreData = (data: IntegrationDataItem | null) => {
  useRetailerStore.getState().setRetailerStoreData(data);
  return { type: "retailerStore/setRetailerStoreData", payload: data };
};

export const setIsError = (isError: boolean) => {
  useRetailerStore.getState().setIsError(isError);
  return { type: "retailerStore/setIsError", payload: isError };
};

export const setIsLoading = (isLoading: boolean) => {
  useRetailerStore.getState().setIsLoading(isLoading);
  return { type: "retailerStore/setIsLoading", payload: isLoading };
};

// Create a mock reducer for Redux compatibility (does nothing since Zustand handles state)
const retailerStoreSlice = {
  name: "retailerStore",
  reducer: (_state = useRetailerStore.getState(), _action: any) => {
    // Return current Zustand state - the actual state management is handled by Zustand
    return useRetailerStore.getState();
  },
  actions: {
    setRetailerStoreId,
    getRetailerStoreId,
    setRetailerStoreData,
    setIsError,
    setIsLoading
  }
};

export { retailerStoreSlice };
export default retailerStoreSlice.reducer;

import { Supplier } from "./apps/supplier/query";
import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { AnyAction, combineReducers } from "redux";
import ConfigReducer from "./apps/config/ConfigSlice";
import ChatsReducer from "./apps/chat/ChatSlice";
import { Auth } from "./apps/auth";
import { Retailer } from "./apps/retailer";
import { Meta, MetaWithPersist } from "./apps/meta";
import { RetailerProduct } from "./apps/retailerProduct";
import { Product } from "./apps/product";
import { Order } from "./apps/order";
import { SupplierDashboard } from "./apps/supplierDashboard";
import RetailerStoreReducer from "./apps/retailerStore/retailerStoreSlice";
import { Conversation } from "./apps/conversation";
import { persistReducer, persistStore, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from "redux-persist";
import { persistConfig } from "./redux-persist";

// Combine all reducers into the rootReducer
const appReducer = combineReducers({
  chatReducer: ChatsReducer,
  config: ConfigReducer,
  retailerStore: RetailerStoreReducer,
  [Auth.reducerPath]: Auth.reducer,
  [Supplier.reducerPath]: Supplier.reducer,
  [Retailer.reducerPath]: Retailer.reducer,
  [Meta.reducerPath]: Meta.reducer,
  [MetaWithPersist.reducerPath]: MetaWithPersist.reducer,
  [RetailerProduct.reducerPath]: RetailerProduct.reducer,
  [Product.reducerPath]: Product.reducer,
  [Order.reducerPath]: Order.reducer,
  [SupplierDashboard.reducerPath]: SupplierDashboard.reducer,
  [Conversation.reducerPath]: Conversation.reducer
});

// Root reducer that handles resetting the state on logout
const rootReducer = (state: AppState | undefined, action: AnyAction) => {
  if (action.type === "RESET") {
    // Clear persisted state on reset
    state = undefined;
  }
  return appReducer(state, action);
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// All API slices for easier management
const apiSlices = [
  Auth,
  Supplier,
  Retailer,
  Meta,
  MetaWithPersist,
  RetailerProduct,
  Product,
  Order,
  SupplierDashboard,
  Conversation
];

// Configure the store with the persistedReducer and middleware
export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore redux-persist actions
        ignoredActions: [
          FLUSH,
          REHYDRATE,
          PAUSE,
          PERSIST,
          PURGE,
          REGISTER,
          // Ignore all RTK Query actions for serialization
          ...apiSlices.flatMap(api => [
            `${api.reducerPath}/executeQuery/pending`,
            `${api.reducerPath}/executeQuery/fulfilled`,
            `${api.reducerPath}/executeQuery/rejected`,
            `${api.reducerPath}/executeMutation/pending`,
            `${api.reducerPath}/executeMutation/fulfilled`,
            `${api.reducerPath}/executeMutation/rejected`
          ])
        ],
        // Ignore RTK Query state paths that contain non-serializable values
        ignoredPaths: [
          ...apiSlices.flatMap(api => [
            `${api.reducerPath}.queries`,
            `${api.reducerPath}.mutations`,
            `${api.reducerPath}.provided`,
            `${api.reducerPath}.subscriptions`,
            `${api.reducerPath}.config`
          ])
        ]
      }
    }).concat(
      // Add all API middlewares
      ...apiSlices.map(api => api.middleware)
    ),
  devTools: process.env.NODE_ENV !== "production"
});

// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type AppState = ReturnType<typeof appReducer>;

// Set up listeners for refetchOnFocus/refetchOnReconnect behaviors
setupListeners(store.dispatch);

// Create persistor
export const persistor = persistStore(store);

// Optional: Add error handling for persist operations
persistor.subscribe(() => {
  // Handle persist state changes if needed
  const { bootstrapped } = persistor.getState();
  if (bootstrapped) {
    console.log("Redux state has been rehydrated");
  }
});

// Reset function to clear all persisted data
export const resetStore = () => {
  persistor.purge();
  store.dispatch({ type: "RESET" });
};
